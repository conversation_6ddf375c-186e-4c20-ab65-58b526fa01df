# JJBugly数据字段说明

# 异常数据字段，ES索引：\*:cd-jjbugly-crash-crash-\*

| **字段** | **数据描述** | **pdcbei-clienterr-\*的字段** |
| --- | --- | --- |
| crashType | 见下表 | errtype(转换为string) |
| crashSignal | 异常名称 | errname |
| crashMessage | 异常描述信息 | errsign |
| appId | 应用Id |  |
| appVersion | 应用版本 | app\_ver |
| appChannel | 应用渠道 |  |
| packageName | 应用包名 | pkg\_name |
| appName | 应用名称 |  |
| isSdkDebugMode | SDK是否配置为调试模式 |  |
| ramAvail | 异常时设备可用内存大小(kb) | ramAvail |
| ramTotal | 设备内存总大小(kb) | ramTotal |
| romAvail | 内部存储可用大小(kb) |  |
| romTotal | 内部存储总大小(kb) |  |
| crashCount | 此条异常发生的次数 |  |
| netType | 异常时网络的类型 |  |
| crashThreadName | 异常线程名称 |  |
| crashProcessName | 异常进程名称 |  |
| crashUuid | 异常唯一id |  |
| deviceModel | 设备型号 | device\_model |
| deviceType | 用户可见的设备型号 | model |
| deviceRom | 设备Rom信息 |  |
| deviceId | 设备id（由sdk内部生成的随机数，卸载后丢失） |  |
| deviceOs | 设备系统版本 | sys\_ver(仅转换了安卓大版本) |
| userId | 用户id | uid |
| userSceneTag | 用户的当前页面的标签 |  |
| deviceCpu | 设备CPU |  |
| deviceArchitecture | 设备架构 | appbit |
| sdkInitTime | SDK初始化时间 |  |
| crashTime | 异常发生时间 | crashTime(转换为字符串) |
| systemLog | 异常时系统log |  |
| userLog | 异常时用户的log |  |
| crashStack | 异常函数栈 | content |
| sdkVersion | JJBugly SDK的版本 |  |
| result.groupId | 分组id | err\_sign\_md5 |
| deviceIpAddress | 设备IP | ip\_addr |
| result.crashModule | 发生异常的模块 | module |
| result.isSymbolicate | 是否经过符号还原 | has\_symbol\_info |
| result.uploadTime | 上传时间 |  |
| deviceCountry | 设备所在国家 |  |
| userKeyValue | 用户自定义字段(不可被索引) |  |
| isAppForeground | 应用是否在前台 | isAppForeground |
| nativeLeakInfo | native内存泄漏信息 |  |
| platform | 设备平台 | platform(转换为字符串) |
| androidInfo.isRoot | 是否是root设备 |  |
| androidInfo.javaStackMap | 异常时java层完整栈信息 |  |
| androidInfo.vmSize | 异常时虚拟内存大小 | androidVmSize |
| androidInfo.fdCount | 异常时文件句柄数 |  |
| androidInfo.threadCount | 异常时线程数 |  |
| androidInfo.isEmulator | 是否为模拟器 |  |
| iosInfo.isJailbroken | 是否越狱 |  |
| iosInfo.appAvailMemory | 异常时应用可用内存(iOS 13.0以上有效) |  |
| customFields | 自定义字段(二级字段，可索引) | 作为一级字段存储 |

## crashType说明

| **数值** | **说明** | **pdcbei-clienterr-\*的errtype** |
| --- | --- | --- |
| 0 | 安卓Java崩溃 | Java Dump |
| 1 | 安卓Java主动上传异常 | Java Error |
| 2 | 安卓Native崩溃 | C++ Dump |
| 3 | 安卓卡顿 | Lag |
| 4 | 安卓 ANR | ANR |
| 5 | Cocos2dx-Js异常 |  |
| 6 | Cocos2dx-Lua异常 |  |
| 7 | Unity C#异常 | C# Error |
| 8 | Unity Log/主动上报 | Unity log |
| 9 | iOS崩溃 | Objective-C Dump |
| 10 | iOS主动上传异常 | OC Exception |
| 11 | iOS卡顿 | Lag |
| 12 | iOS卡死崩溃 | Block Crash |
| 13 | iOS FOOM | FOOM |
| 14 | Windows崩溃 |  |
| 15 | Windows卡死 |  |
| 16 | 鸿蒙Native崩溃 |  |
| 17 | 鸿蒙Js崩溃 |  |
| 18 | 鸿蒙Js已捕获错误 |  |
| 19 | 鸿蒙Freeze |  |
| 20 | JS异常（JJ大厅） | Js Error |
| 21 | Java Log | Java Log |
| 22 | 鸿蒙资源泄漏 |  |
| 23 | 鸿蒙主线程超时 |  |
| 24 | 鸿蒙踩内存 |  |
| 25 | 安卓tombstone附件 |  |
| 26 | 安卓tombstone崩溃 |  |
| 27 | iOS MetricKit异常诊断 | MetricKit Exception |
| 28 | iOS MetricKit性能报告 | MetricKit Performance |

## 大异常类型说明（异常趋势中的类型）

| **类型** | **crashType** |
| --- | --- |
| 崩溃 | 0/2/9/14/16/17/24 |
| 卡顿 | 3/11/23 |
| 错误 | 5/6/7/20/22 |
| 已捕获异常 | 1/8/10/18/21 |
| ANR/卡死 | 4/12/15/19/ |
| 暂未分类的类型 | 13/27/28 |

# 心跳数据字段，ES索引：\*:cd-jjbugly-crash-hb-\*

| **字段** | **数据描述** |
| --- | --- |
| appId | 应用Id |
| appVersion | 应用版本 |
| appChannel | 渠道 |
| deviceId | 设备id |
| deviceModel | 设备型号 |
| deviceOs | 设备系统版本 |
| launchIndex | 应用冷启动次数 |
| userId | 用户id |
| isStart | 是否为启动时心跳 |
| timeStamp | 心跳上报时间 |

