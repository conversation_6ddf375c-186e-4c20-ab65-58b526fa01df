CREATE DATABASE IF NOT EXISTS jjbugly DEFAULT CHARACTER SET utf8 DEFAULT COLLATE utf8_general_ci;

CREATE TABLE `jjbugly_crash_crash` (
  `pt_dt` DATE NOT NULL,
  `md5` VARCHAR(256) NULL,
  `crash_appId` VARCHAR(255) NULL,
  `crash_crashTime` BIGINT NULL,
  `crash_appVersion` VARCHAR(255) NULL,
  `crash_appChannel` VARCHAR(255) NULL,
  `crash_packageName` VARCHAR(255) NULL,
  `crash_crashType` VARCHAR(255) NULL,
  `crash_deviceId` VARCHAR(255) NULL,
  `crash_deviceModel` VARCHAR(255) NULL,
  `crash_userId` VARCHAR(255) NULL,
  `crash_isAppForeground` VARCHAR(255) NULL,
  `crash_customFields_compile_time` VARCHAR(255) NULL,
  `crash_customFields_tkgameid` VARCHAR(255) NULL,
  `crash_customFields_cur_gameid` VARCHAR(255) NULL,
  `crash_customFields_promoterid` VARCHAR(255) NULL,
  `crash_crashCount` INT NULL,
  `crash_crashSignal` VARCHAR(255) NULL,
  `crash_crashMessage` TEXT NULL,
  `result_crashModule` VARCHAR(255) NULL,
  `result_groupId` VARCHAR(255) NULL,
  `result` JSON NULL,
  `crash` JSON NULL,
  `iosInfo` JSON NULL,
  `androidInfo` JSON NULL,
  `windowsInfo` JSON NULL,
  `harmonyInfo` JSON NULL,
  `crash_customFields_branch` VARCHAR(255) NULL,
  `crash_customFields_cur_subgameid` VARCHAR(255) NULL
) ENGINE=OLAP
UNIQUE KEY(`pt_dt`, `md5`)
COMMENT '异常信息表'
PARTITION BY RANGE(`pt_dt`);

CREATE TABLE `jjbugly_crash_hb` (
  `pt_dt` DATE NULL,
  `md5` VARCHAR(256) NULL,
  `appId` VARCHAR(255) NULL,
  `appChannel` VARCHAR(255) NULL,
  `appVersion` VARCHAR(255) NULL,
  `gameId` VARCHAR(255) NULL,
  `deviceId` VARCHAR(255) NULL,
  `deviceModel` VARCHAR(255) NULL,
  `deviceOs` VARCHAR(255) NULL,
  `userId` VARCHAR(255) NULL,
  `isStart` VARCHAR(255) NULL,
  `launchIndex` VARCHAR(255) NULL,
  `timeStamp` BIGINT NULL
) ENGINE=OLAP
UNIQUE KEY(`pt_dt`, `md5`)
COMMENT '心跳信息表'
PARTITION BY RANGE(`pt_dt`);

