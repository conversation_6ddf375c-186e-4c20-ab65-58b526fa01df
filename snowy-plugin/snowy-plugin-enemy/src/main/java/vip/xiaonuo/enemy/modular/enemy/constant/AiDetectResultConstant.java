package vip.xiaonuo.enemy.modular.enemy.constant;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * AI检测结果常量类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/01/02 11:30
 */
public class AiDetectResultConstant {

    /**
     * 合法的AI检测结果分类
     */
    public static final Set<String> VALID_CATEGORIES = new HashSet<>(Arrays.asList(
            "买卖/租赁账号",
            "买卖玩家信息",
            "买卖金币",
            "人脸破解",
            "刷金软件",
            "带团",
            "篡改客户端",
            "转金工具",
            "高风险举报",
            "未知",
            "正常",
            "游戏代练"
    ));

    /**
     * 无法识别分类
     */
    public static final String UNRECOGNIZED_CATEGORY = "无法识别";

    /**
     * 正常分类
     */
    public static final String NORMAL_CATEGORY = "正常";

    /**
     * 未知分类
     */
    public static final String UNKNOWN_CATEGORY = "未知";

    /**
     * 结果代码 - 异常内容
     */
    public static final String RESULT_CODE_ABNORMAL = "1";

    /**
     * 结果代码 - 正常内容
     */
    public static final String RESULT_CODE_NORMAL = "2";

    /**
     * 结果代码 - 未知内容
     */
    public static final String RESULT_CODE_UNKNOWN = "3";

    /**
     * 邮件过滤分类 - 这些分类不会出现在每日汇总邮件中
     */
    public static final Set<String> EMAIL_FILTERED_CATEGORIES = new HashSet<>(Arrays.asList(
            NORMAL_CATEGORY,
            UNKNOWN_CATEGORY,
            UNRECOGNIZED_CATEGORY
    ));

    /**
     * 验证AI检测结果是否为合法分类
     *
     * @param detectResult AI检测结果
     * @return 如果是合法分类返回原结果，否则返回"无法识别"
     */
    public static String validateAndNormalize(String detectResult) {
        if (VALID_CATEGORIES.contains(detectResult)) {
            return detectResult;
        } else {
            return UNRECOGNIZED_CATEGORY;
        }
    }

    /**
     * 判断检测结果是否应该在邮件中被过滤
     *
     * @param detectResult AI检测结果
     * @return true表示应该被过滤（不出现在邮件中），false表示应该保留
     */
    public static boolean shouldFilterInEmail(String detectResult) {
        return EMAIL_FILTERED_CATEGORIES.stream()
                .anyMatch(category -> category.equalsIgnoreCase(detectResult));
    }

    /**
     * 获取检测结果对应的代码
     *
     * @param detectResult AI检测结果
     * @return 结果代码
     */
    public static String getResultCode(String detectResult) {
        if (NORMAL_CATEGORY.equalsIgnoreCase(detectResult)) {
            return RESULT_CODE_NORMAL;
        } else if (UNKNOWN_CATEGORY.equalsIgnoreCase(detectResult) ||
                UNRECOGNIZED_CATEGORY.equalsIgnoreCase(detectResult)) {
            return RESULT_CODE_UNKNOWN;
        } else {
            return RESULT_CODE_ABNORMAL;
        }
    }
}
