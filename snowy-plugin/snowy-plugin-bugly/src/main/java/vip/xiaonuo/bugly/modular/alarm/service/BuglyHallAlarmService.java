/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.bugly.modular.alarm.service;

import vip.xiaonuo.bugly.modular.alarm.dto.BuglyHallAlarmDataDTO;
import vip.xiaonuo.bugly.modular.alarm.dto.BuglyHallAlarmResultDTO;
import vip.xiaonuo.bugly.modular.alarm.entity.BuglyHallAlarm;

import java.util.List;

/**
 * 大厅告警Service接口
 *
 * <AUTHOR>
 * @date 2025/08/07 16:00
 **/
public interface BuglyHallAlarmService {

    /**
     * 从JSON文件加载告警配置
     *
     * @return 告警配置列表
     * <AUTHOR>
     * @date 2025/08/07 16:00
     */
    List<BuglyHallAlarm> loadAlarmConfigFromJson();

    /**
     * 查询Doris获取告警数据
     *
     * @param appId      应用ID
     * @param crashTypes 异常类型列表
     * @param dayHour    统计时间（格式：yyyy-MM-dd HH:mm:ss）
     * @return 告警数据
     * <AUTHOR>
     * @date 2025/08/07 16:00
     */
    BuglyHallAlarmDataDTO queryAlarmDataFromDoris(String appId, List<String> crashTypes, String dayHour);

    /**
     * 检查是否触发告警条件
     *
     * @param alarmConfig 告警配置
     * @param alarmData   告警数据
     * @return 是否触发告警及触发条件描述
     * <AUTHOR>
     * @date 2025/08/07 16:00
     */
    BuglyHallAlarmResultDTO checkAlarmConditions(BuglyHallAlarm alarmConfig, List<BuglyHallAlarmDataDTO> alarmData);

    /**
     * 发送大厅告警(小时)
     *
     * @param dayHour 统计时间（格式：yyyy-MM-dd HH:mm:ss，默认当前时间减1小时）
     * @return 告警结果列表
     * <AUTHOR>
     * @date 2025/08/07 16:00
     */
    List<BuglyHallAlarmResultDTO> sendHallHourAlarm(String dayHour);

    /**
     * 生成告警消息内容
     *
     * @param alarmResult 告警结果
     * @return 告警消息内容
     * <AUTHOR>
     * @date 2025/08/07 16:00
     */
    String generateAlarmMessage(BuglyHallAlarmResultDTO alarmResult);


}
