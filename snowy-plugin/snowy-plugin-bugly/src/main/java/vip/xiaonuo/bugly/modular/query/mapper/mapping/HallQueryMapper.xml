<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.bugly.modular.query.mapper.HallQueryMapper">

    <!-- 监控数据传输对象类 -->
    <resultMap type="vip.xiaonuo.bugly.modular.monitor.entity.MonitorBuglyHour"
               id="monitorBuglyHourResultMap">
        <!-- 其他字段符合驼峰命名，自动匹配 -->
    </resultMap>

    <!-- 根据时间查询异常数据 -->
    <select id="listExceptionDataMonitor" resultMap="monitorBuglyHourResultMap">
        SELECT
        COUNT(1) as cnt_data_total
        ,COUNT(DISTINCT crash_deviceId) as cnt_user_total
        FROM jjbugly_crash_crash
        -- WHERE pt_dt BETWEEN '2025-08-07' AND '2025-08-08'
        -- AND crash_crashTime BETWEEN 1754625600000 AND 1754629200000
        -- AND crash_appId IN ('ccb40a6032')
        -- 排除测试渠道
        -- AND crash_appChannel NOT IN ('99999','999999','66666','666666','999998','999997')
        -- AND crash_customFields_cur_gameid IN ('')
        -- AND result_crashModule IN ('')
        <trim prefix="WHERE" prefixOverrides="AND|OR">
            <if test="startPtDt!=null and startPtDt!=''">
                AND pt_dt&gt;=#{startPtDt}
            </if>
            <if test="endPtDt!=null and endPtDt!=''">
                AND pt_dt&lt;=#{endPtDt}
            </if>
            <if test="startTime!=null">
                AND crash_crashTime&gt;=#{startTime}
            </if>
            <if test="endTime!=null">
                AND crash_crashTime&lt;=#{endTime}
            </if>
            <if test="appIds != null and appIds.size()>0">
                AND crash_appId in (
                <foreach collection="appIds" item="appId" index="index" separator=",">
                    #{appId}
                </foreach>
                )
            </if>
            -- 排除测试渠道
            AND crash_appChannel NOT IN ('99999','999999','66666','666666','999998','999997')
            <if test="curGameIds != null and curGameIds.size()>0">
                AND crash_customFields_cur_gameid in (
                <foreach collection="curGameIds" item="curGameId" index="index" separator=",">
                    #{curGameId}
                </foreach>
                )
            </if>
            <if test="crashModules != null and crashModules.size()>0">
                AND result_crashModule in (
                <foreach collection="crashModules" item="crashModule" index="index" separator=",">
                    #{crashModule}
                </foreach>
                )
            </if>
        </trim>
    </select>

    <!-- 根据时间查询心跳数据 -->
    <select id="listHeartbeatDataMonitor" resultMap="monitorBuglyHourResultMap">
        SELECT
        COUNT(1) as cnt_heartbeat_launch
        ,COUNT(DISTINCT deviceId) as cnt_heartbeat_user
        FROM jjbugly_crash_hb
        -- WHERE pt_dt BETWEEN '2025-08-07' AND '2025-08-08'
        -- AND timeStamp BETWEEN 1754625600000 AND 1754629200000
        -- AND appId IN ('ccb40a6032')
        -- 排除测试渠道
        -- AND appChannel NOT IN ('99999','999999','66666','666666','999998','999997')
        <trim prefix="WHERE" prefixOverrides="AND|OR">
            <if test="startPtDt!=null and startPtDt!=''">
                AND pt_dt&gt;=#{startPtDt}
            </if>
            <if test="endPtDt!=null and endPtDt!=''">
                AND pt_dt&lt;=#{endPtDt}
            </if>
            <if test="startTime!=null">
                AND timeStamp&gt;=#{startTime}
            </if>
            <if test="endTime!=null">
                AND timeStamp&lt;=#{endTime}
            </if>
            <if test="appIds != null and appIds.size()>0">
                AND appId in (
                <foreach collection="appIds" item="appId" index="index" separator=",">
                    #{appId}
                </foreach>
                )
            </if>
            -- 排除测试渠道
            AND appChannel NOT IN ('99999','999999','66666','666666','999998','999997')
        </trim>
    </select>
</mapper>