/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.bugly.modular.alarm.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 大厅告警配置实体
 *
 * <AUTHOR>
 * @date 2025/08/07 16:00
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "大厅告警配置实体")
public class BuglyHallAlarm implements Serializable {

    private static final Long serialVersionUID = 1L;

    /**
     * 告警ID
     */
    @ApiModelProperty(value = "告警ID", name = "id")
    private String id;

    /**
     * 告警名称
     */
    @ApiModelProperty(value = "告警名称", name = "alarmName")
    private String alarmName;

    /**
     * 应用ID列表
     */
    @ApiModelProperty(value = "应用ID列表", position = 3)
    private List<String> appIds;

    /**
     * 异常类型列表（可多选，合并计算）
     */
    @ApiModelProperty(value = "异常类型列表", position = 4)
    private List<String> crashTypes;

    /**
     * 接收人员列表
     */
    @ApiModelProperty(value = "接收人员列表", position = 5)
    private List<String> receivers;

    /**
     * 告警条件配置
     */
    @ApiModelProperty(value = "告警条件配置", position = 6)
    private BuglyHallAlarmCondition conditions;

    /**
     * 告警状态（0-禁用，1-启用）
     */
    @ApiModelProperty(value = "告警状态", position = 7)
    private Integer status;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间", position = 8)
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", position = 9)
    private Date updateTime;

    /**
     * 大厅告警条件配置
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @ApiModel(description = "大厅告警条件配置")
    public static class BuglyHallAlarmCondition implements Serializable {

        private static final Long serialVersionUID = 1L;

        /**
         * 过去24小时设备数阈值
         */
        @ApiModelProperty(value = "过去24小时设备数阈值", name = "deviceCountThreshold")
        private Integer deviceCountThreshold;

        /**
         * 过去24小时设备率阈值（百分比）
         */
        @ApiModelProperty(value = "过去24小时设备率阈值", name = "deviceRateThreshold")
        private Double deviceRateThreshold;

        /**
         * 此小时相比上一日同时段增长率阈值（百分比）
         */
        @ApiModelProperty(value = "此小时相比上一日同时段增长率阈值", name = "growthRateThreshold")
        private Double growthRateThreshold;
    }
}
