package vip.xiaonuo.bugly.modular.query.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import vip.xiaonuo.bugly.core.constant.DataSourceConstant;
import vip.xiaonuo.bugly.modular.monitor.entity.MonitorBuglyHour;
import vip.xiaonuo.bugly.modular.query.mapper.HallQueryMapper;
import vip.xiaonuo.bugly.modular.query.service.HallQueryService;
import vip.xiaonuo.bugly.modular.syncInfo.entity.AppInfo;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN;

/**
 * Description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/8/8 13:43
 */
@Service
@DS(DataSourceConstant.DYNAMIC_DATASOURCE_DORIS)
@Slf4j
public class HallQueryServiceImpl implements HallQueryService {

    @Resource
    private HallQueryMapper hallQueryMapper;

    @Override
    public List<MonitorBuglyHour> listExceptionDataMonitor(String dayHour, String dayOrHour, List<String> appIds, List<String> curGameIds, List<String> crashModules) {
        try {
            // dayOrHour =day按天统计，=hour 按小时统计，默认小时
            dayOrHour = StringUtils.defaultIfBlank(dayOrHour, "hour");
            Date date = null;
            Long startTime = null;
            Long endTime = null;
            String startPtDt = null;
            String endPtDt = null;
            if (StringUtils.equalsIgnoreCase(dayOrHour, "day")) {
                date = StringUtils.isBlank(dayHour) ? DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -1)).toJdkDate() : DateUtil.beginOfDay(DateUtil.parse(dayHour)).toJdkDate();
                startTime = DateUtil.beginOfDay(date).getTime();
                endTime = DateUtil.beginOfDay(DateUtil.offsetDay(date, 1)).getTime();
                startPtDt = DateUtil.format(DateUtil.date(startTime), DatePattern.NORM_DATE_PATTERN);
                endPtDt = DateUtil.format(DateUtil.date(endTime), DatePattern.NORM_DATE_PATTERN);
            } else if (StringUtils.equalsIgnoreCase(dayOrHour, "hour")) {
                date = StringUtils.isBlank(dayHour) ? DateUtil.beginOfHour(DateUtil.offsetHour(new Date(), -1)).toJdkDate() : DateUtil.beginOfHour(DateUtil.parse(dayHour)).toJdkDate();
                startTime = DateUtil.beginOfHour(date).getTime();
                endTime = DateUtil.beginOfHour(DateUtil.offsetHour(date, 1)).getTime();
                startPtDt = DateUtil.format(DateUtil.date(startTime), DatePattern.NORM_DATE_PATTERN);
                endPtDt = DateUtil.format(DateUtil.date(endTime), DatePattern.NORM_DATE_PATTERN);
            } else {
                log.warn("不支持dayOrHour=[{}]统计类型!", dayOrHour);
                return new ArrayList<>();
            }

            String day = DateUtil.format(date, "yyyy-MM-dd");
            Integer hour = DateUtil.hour(date, Boolean.TRUE);
            // 时间类型(小时=hour,天=day)
            String timeType = dayOrHour;

            log.info("根据时间查询异常数据(天或小时)(Doris)，dayHour=[{}], dayOrHour=[{}], appIds=[{}], curGameIds=[{}], crashModules=[{}], startPtDt=[{}], endPtDt=[{}], startTime=[{}], endTime=[{}], day=[{}], hour=[{}]", dayHour, dayOrHour, JSONObject.toJSONString(appIds), JSONObject.toJSONString(curGameIds), JSONObject.toJSONString(crashModules), startPtDt, endPtDt, startTime, endTime, day, hour);

            LocalDateTime queryStartTime = LocalDateTime.now();
            List<MonitorBuglyHour> result = hallQueryMapper.listExceptionDataMonitor(startPtDt, endPtDt, startTime, endTime, appIds, curGameIds, crashModules);
            LocalDateTime queryEndTime = LocalDateTime.now();
            log.info("根据时间查询异常数据(天或小时)(Doris)完成，耗时=[{}]ms，结果数量=[{}]", Duration.between(queryStartTime, queryEndTime).toMillis(), CollectionUtils.size(result));

            return CollectionUtils.isNotEmpty(result) ? result : new ArrayList<>();

        } catch (Exception e) {
            log.error("根据时间查询异常数据(天或小时)(Doris)失败，dayHour=[{}], dayOrHour=[{}], appIds=[{}], curGameIds=[{}], crashModules=[{}]", dayHour, dayOrHour, JSONObject.toJSONString(appIds), JSONObject.toJSONString(curGameIds), JSONObject.toJSONString(crashModules), e);
        }
        return new ArrayList<>();
    }

    @Override
    public List<MonitorBuglyHour> listHeartbeatDataMonitor(String dayHour, String dayOrHour, List<String> appIds) {
        try {
            // dayOrHour =day按天统计，=hour 按小时统计，默认小时
            dayOrHour = StringUtils.defaultIfBlank(dayOrHour, "hour");
            Date date = null;
            Long startTime = null;
            Long endTime = null;
            String startPtDt = null;
            String endPtDt = null;
            if (StringUtils.equalsIgnoreCase(dayOrHour, "day")) {
                date = StringUtils.isBlank(dayHour) ? DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -1)).toJdkDate() : DateUtil.beginOfDay(DateUtil.parse(dayHour)).toJdkDate();
                startTime = DateUtil.beginOfDay(date).getTime();
                endTime = DateUtil.beginOfDay(DateUtil.offsetDay(date, 1)).getTime();
                startPtDt = DateUtil.format(DateUtil.date(startTime), DatePattern.NORM_DATE_PATTERN);
                endPtDt = DateUtil.format(DateUtil.date(endTime), DatePattern.NORM_DATE_PATTERN);
            } else if (StringUtils.equalsIgnoreCase(dayOrHour, "hour")) {
                date = StringUtils.isBlank(dayHour) ? DateUtil.beginOfHour(DateUtil.offsetHour(new Date(), -1)).toJdkDate() : DateUtil.beginOfHour(DateUtil.parse(dayHour)).toJdkDate();
                startTime = DateUtil.beginOfHour(date).getTime();
                endTime = DateUtil.beginOfHour(DateUtil.offsetHour(date, 1)).getTime();
                startPtDt = DateUtil.format(DateUtil.date(startTime), DatePattern.NORM_DATE_PATTERN);
                endPtDt = DateUtil.format(DateUtil.date(endTime), DatePattern.NORM_DATE_PATTERN);
            } else {
                log.warn("不支持dayOrHour=[{}]统计类型!", dayOrHour);
                return new ArrayList<>();
            }

            String day = DateUtil.format(date, "yyyy-MM-dd");
            Integer hour = DateUtil.hour(date, Boolean.TRUE);
            // 时间类型(小时=hour,天=day)
            String timeType = dayOrHour;

            log.info("根据时间查询心跳数据(天或小时)(Doris)，dayHour=[{}], dayOrHour=[{}], appIds=[{}], startPtDt=[{}], endPtDt=[{}], startTime=[{}], endTime=[{}], day=[{}], hour=[{}]", dayHour, dayOrHour, JSONObject.toJSONString(appIds), startPtDt, endPtDt, startTime, endTime, day, hour);

            LocalDateTime queryStartTime = LocalDateTime.now();
            List<MonitorBuglyHour> result = hallQueryMapper.listHeartbeatDataMonitor(startPtDt, endPtDt, startTime, endTime, appIds);
            LocalDateTime queryEndTime = LocalDateTime.now();
            log.info("根据时间查询心跳数据(天或小时)(Doris)完成，耗时=[{}]ms，结果数量=[{}]", Duration.between(queryStartTime, queryEndTime).toMillis(), CollectionUtils.size(result));

            return CollectionUtils.isNotEmpty(result) ? result : new ArrayList<>();

        } catch (Exception e) {
            log.error("根据时间查询心跳数据(天或小时)(Doris)失败，dayHour=[{}], dayOrHour=[{}], appIds=[{}]", dayHour, dayOrHour, JSONObject.toJSONString(appIds), e);
        }
        return new ArrayList<>();
    }
}
