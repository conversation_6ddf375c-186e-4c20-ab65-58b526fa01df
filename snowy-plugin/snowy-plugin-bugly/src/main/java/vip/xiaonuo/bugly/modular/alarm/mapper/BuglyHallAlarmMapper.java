/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.bugly.modular.alarm.mapper;

import org.apache.ibatis.annotations.Param;
import vip.xiaonuo.bugly.modular.alarm.dto.BuglyHallAlarmDataDTO;

import java.util.List;

/**
 * 大厅告警Mapper接口
 *
 * <AUTHOR>
 * @date 2025/08/07 16:00
 **/
public interface BuglyHallAlarmMapper {

    /**
     * 查询过去24小时异常设备数据
     *
     * @param appId      应用ID
     * @param crashTypes 异常类型列表（逗号分隔）
     * @param startTime  开始时间（毫秒时间戳）
     * @param endTime    结束时间（毫秒时间戳）
     * @return 异常设备数据
     */
    BuglyHallAlarmDataDTO queryPast24HourCrashData(@Param("appId") String appId, @Param("crashTypes") String crashTypes, @Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 查询过去24小时总设备数
     *
     * @param appId     应用ID
     * @param startTime 开始时间（毫秒时间戳）
     * @param endTime   结束时间（毫秒时间戳）
     * @return 总设备数
     */
    Long queryPast24HourTotalDeviceCnt(@Param("appId") String appId, @Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * 查询指定时间段异常设备数
     *
     * @param appId      应用ID
     * @param crashTypes 异常类型列表（逗号分隔）
     * @param startTime  开始时间（毫秒时间戳）
     * @param endTime    结束时间（毫秒时间戳）
     * @return 异常设备数
     */
    Long queryHourCrashDeviceCnt(@Param("appId") String appId, @Param("crashTypes") String crashTypes, @Param("startTime") Long startTime, @Param("endTime") Long endTime);
}
