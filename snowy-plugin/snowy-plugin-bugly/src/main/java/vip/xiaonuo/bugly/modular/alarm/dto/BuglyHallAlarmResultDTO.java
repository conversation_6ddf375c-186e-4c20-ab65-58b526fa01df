/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.bugly.modular.alarm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vip.xiaonuo.bugly.modular.alarm.entity.BuglyHallAlarm;

import java.io.Serializable;
import java.util.List;

/**
 * 大厅告警结果传输对象
 *
 * <AUTHOR>
 * @date 2025/08/07 16:00
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "大厅告警结果传输对象")
public class BuglyHallAlarmResultDTO implements Serializable {

    private static final Long serialVersionUID = 1L;

    /**
     * 告警配置
     */
    @ApiModelProperty(value = "告警配置", position = 1)
    private BuglyHallAlarm alarmConfig;

    /**
     * 告警数据
     */
    @ApiModelProperty(value = "告警数据", position = 2)
    private List<BuglyHallAlarmDataDTO> alarmDataList;

    /**
     * 是否触发告警
     */
    @ApiModelProperty(value = "是否触发告警", position = 3)
    private Boolean isTriggered;

    /**
     * 触发的告警条件描述
     */
    @ApiModelProperty(value = "触发的告警条件描述", position = 4)
    private List<String> triggeredConditions;

    /**
     * 告警消息内容
     */
    @ApiModelProperty(value = "告警消息内容", position = 5)
    private String alarmMessage;

    /**
     * 统计时间
     */
    @ApiModelProperty(value = "统计时间", position = 6)
    private String statisticsTime;
}
