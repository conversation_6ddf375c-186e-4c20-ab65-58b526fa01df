/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.bugly.modular.alarm.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import vip.xiaonuo.bugly.core.constant.DataSourceConstant;
import vip.xiaonuo.bugly.modular.alarm.dto.BuglyHallAlarmDataDTO;
import vip.xiaonuo.bugly.modular.alarm.dto.BuglyHallAlarmResultDTO;
import vip.xiaonuo.bugly.modular.alarm.entity.BuglyHallAlarm;
import vip.xiaonuo.bugly.modular.alarm.mapper.BuglyHallAlarmMapper;
import vip.xiaonuo.bugly.modular.alarm.service.BuglyHallAlarmService;
import vip.xiaonuo.bugly.modular.message.service.MessageService;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 大厅告警Service接口实现类
 *
 * <AUTHOR>
 * @date 2025/08/07 16:00
 **/
@Service
@DS(DataSourceConstant.DYNAMIC_DATASOURCE_DORIS)
@Slf4j
public class BuglyHallAlarmServiceImpl implements BuglyHallAlarmService {

    /**
     * 一小时的毫秒数
     */
    private static final long HOUR_MILLIS = 3600000L;

    /**
     * 一天的毫秒数
     */
    private static final long DAY_MILLIS = 24 * HOUR_MILLIS;

    @Resource
    private MessageService messageService;

    @Autowired
    private BuglyHallAlarmMapper buglyHallAlarmMapper;

    /**
     * 构建异常类型条件字符串
     *
     * @param crashTypes 异常类型列表
     * @return 异常类型条件字符串
     */
    private String buildCrashTypeCondition(List<String> crashTypes) {
        return crashTypes.stream()
            .map(type -> "'" + type + "'")
            .collect(Collectors.joining(","));
    }

    @Override
    public List<BuglyHallAlarm> loadAlarmConfigFromJson() {
        try {
            ClassPathResource resource = new ClassPathResource("hall-alarm-config.json");
            if (!resource.exists()) {
                log.warn("大厅告警配置文件不存在: hall-alarm-config.json");
                return new ArrayList<>();
            }
            
            String jsonContent = new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
            JSONArray jsonArray = JSONObject.parseArray(jsonContent);
            
            List<BuglyHallAlarm> alarmConfigs = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                BuglyHallAlarm alarmConfig = JSONObject.toJavaObject(jsonObject, BuglyHallAlarm.class);
                alarmConfigs.add(alarmConfig);
            }
            
            log.info("成功加载大厅告警配置，共{}条", alarmConfigs.size());
            return alarmConfigs;
        } catch (IOException e) {
            log.error("加载大厅告警配置文件失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public BuglyHallAlarmDataDTO queryAlarmDataFromDoris(String appId, List<String> crashTypes, String dayHour) {
        Date date = StringUtils.isBlank(dayHour) ? DateUtil.offsetHour(new Date(), -1).toJdkDate() : DateUtil.parse(dayHour).toJdkDate();
        Long endTime = date.getTime();
        Long startTime = endTime - HOUR_MILLIS;

        BuglyHallAlarmDataDTO alarmData = new BuglyHallAlarmDataDTO();
        alarmData.setAppId(appId);
        alarmData.setCrashTypes(crashTypes);
        alarmData.setStatisticsTime(DateUtil.format(date, DatePattern.NORM_DATETIME_PATTERN));

        // 查询过去24小时数据
        BuglyHallAlarmDataDTO past24HourData = queryPast24HourData(appId, crashTypes, endTime);
        alarmData.setPast24HourCrashDeviceCnt(past24HourData.getPast24HourCrashDeviceCnt());
        alarmData.setPast24HourTotalDeviceCnt(past24HourData.getPast24HourTotalDeviceCnt());
        alarmData.setPast24HourCrashDeviceRate(past24HourData.getPast24HourCrashDeviceRate());

        // 查询当前小时数据
        Long currentHourDeviceCount = queryHourCrashDeviceCount(appId, crashTypes, startTime, endTime);
        alarmData.setCurrentHourCrashDeviceCnt(currentHourDeviceCount);

        // 查询上一日同时段数据
        Long lastDayStartTime = startTime - DAY_MILLIS;
        Long lastDayEndTime = endTime - DAY_MILLIS;
        Long lastDaySameHourDeviceCount = queryHourCrashDeviceCount(appId, crashTypes, lastDayStartTime, lastDayEndTime);
        alarmData.setLastDaySameHourCrashDeviceCnt(lastDaySameHourDeviceCount);

        // 计算增长率
        if (lastDaySameHourDeviceCount != null && lastDaySameHourDeviceCount > 0) {
            BigDecimal growthRate = BigDecimal.valueOf(currentHourDeviceCount - lastDaySameHourDeviceCount)
                .multiply(BigDecimal.valueOf(100))
                .divide(BigDecimal.valueOf(lastDaySameHourDeviceCount), 2, RoundingMode.HALF_UP);
            alarmData.setGrowthRate(growthRate);
        } else {
            alarmData.setGrowthRate(currentHourDeviceCount > 0 ? BigDecimal.valueOf(100) : BigDecimal.ZERO);
        }

        return alarmData;
    }

    @Override
    public BuglyHallAlarmResultDTO checkAlarmConditions(BuglyHallAlarm alarmConfig, List<BuglyHallAlarmDataDTO> alarmDataList) {
        BuglyHallAlarmResultDTO result = new BuglyHallAlarmResultDTO();
        result.setAlarmConfig(alarmConfig);
        result.setAlarmDataList(alarmDataList);
        result.setIsTriggered(false);
        result.setTriggeredConditions(new ArrayList<>());

        if (CollectionUtils.isEmpty(alarmDataList)) {
            return result;
        }

        BuglyHallAlarm.BuglyHallAlarmCondition conditions = alarmConfig.getConditions();
        if (conditions == null) {
            return result;
        }

        List<String> triggeredConditions = new ArrayList<>();
        boolean isTriggered = false;

        for (BuglyHallAlarmDataDTO alarmData : alarmDataList) {
            // 检查过去24小时设备数阈值
            if (conditions.getDeviceCountThreshold() != null &&
                alarmData.getPast24HourCrashDeviceCnt() != null &&
                alarmData.getPast24HourCrashDeviceCnt() > conditions.getDeviceCountThreshold()) {
                triggeredConditions.add(String.format("应用%s过去24小时异常设备数(%d)超过阈值(%d)",
                    alarmData.getAppId(), alarmData.getPast24HourCrashDeviceCnt(), conditions.getDeviceCountThreshold()));
                isTriggered = true;
            }

            // 检查过去24小时设备率阈值
            if (conditions.getDeviceRateThreshold() != null &&
                alarmData.getPast24HourCrashDeviceRate() != null &&
                alarmData.getPast24HourCrashDeviceRate().compareTo(BigDecimal.valueOf(conditions.getDeviceRateThreshold())) > 0) {
                triggeredConditions.add(String.format("应用%s过去24小时异常设备率(%.2f%%)超过阈值(%.2f%%)",
                    alarmData.getAppId(), alarmData.getPast24HourCrashDeviceRate(), conditions.getDeviceRateThreshold()));
                isTriggered = true;
            }

            // 检查增长率阈值
            if (conditions.getGrowthRateThreshold() != null &&
                alarmData.getGrowthRate() != null &&
                alarmData.getGrowthRate().compareTo(BigDecimal.valueOf(conditions.getGrowthRateThreshold())) > 0) {
                triggeredConditions.add(String.format("应用%s此小时异常设备数相比上一日同时段增长率(%.2f%%)超过阈值(%.2f%%)",
                    alarmData.getAppId(), alarmData.getGrowthRate(), conditions.getGrowthRateThreshold()));
                isTriggered = true;
            }
        }

        result.setIsTriggered(isTriggered);
        result.setTriggeredConditions(triggeredConditions);

        if (isTriggered) {
            result.setAlarmMessage(generateAlarmMessage(result));
        }

        return result;
    }

    @Override
    public List<BuglyHallAlarmResultDTO> sendHallHourAlarm(String dayHour) {
        try {
            log.info("开始执行大厅告警检测，统计时间：{}", dayHour);

            // 加载告警配置
            List<BuglyHallAlarm> alarmConfigs = loadAlarmConfigFromJson();
            if (CollectionUtils.isEmpty(alarmConfigs)) {
                log.warn("未找到大厅告警配置，跳过告警检测");
                return new ArrayList<>();
            }

            List<BuglyHallAlarmResultDTO> alarmResults = new ArrayList<>();

            for (BuglyHallAlarm alarmConfig : alarmConfigs) {
                if (alarmConfig.getStatus() == null || !alarmConfig.getStatus().equals(1)) {
                    log.debug("告警配置已禁用，跳过：{}", alarmConfig.getAlarmName());
                    continue;
                }

                try {
                    // 查询各应用的告警数据
                    List<BuglyHallAlarmDataDTO> alarmDataList = new ArrayList<>();
                    for (String appId : alarmConfig.getAppIds()) {
                        BuglyHallAlarmDataDTO alarmData = queryAlarmDataFromDoris(appId, alarmConfig.getCrashTypes(), dayHour);
                        alarmDataList.add(alarmData);
                    }

                    // 检查告警条件
                    BuglyHallAlarmResultDTO alarmResult = checkAlarmConditions(alarmConfig, alarmDataList);
                    alarmResult.setStatisticsTime(StringUtils.isBlank(dayHour) ?
                        DateUtil.format(DateUtil.offsetHour(new Date(), -1), "yyyy-MM-dd HH:mm:ss") : dayHour);

                    alarmResults.add(alarmResult);

                    // 如果触发告警，发送通知
                    if (alarmResult.getIsTriggered()) {
                        sendAlarmNotification(alarmResult);
                        log.info("大厅告警已触发并发送通知：{}", alarmConfig.getAlarmName());
                    }

                } catch (Exception e) {
                    log.error("处理大厅告警配置失败：{}", alarmConfig.getAlarmName(), e);
                }
            }

            log.info("大厅告警检测完成，共处理{}个配置，触发{}个告警",
                alarmConfigs.size(), alarmResults.stream().mapToInt(r -> r.getIsTriggered() ? 1 : 0).sum());

            return alarmResults;

        } catch (Exception e) {
            log.error("执行大厅告警检测失败", e);
            messageService.sendXingeAdministratorMessage("执行大厅告警检测失败：" + e.getMessage(), null);
            return new ArrayList<>();
        }
    }

    @Override
    public String generateAlarmMessage(BuglyHallAlarmResultDTO alarmResult) {
        StringBuilder message = new StringBuilder();
        message.append("【大厅异常告警】\n");
        message.append("告警名称：").append(alarmResult.getAlarmConfig().getAlarmName()).append("\n");
        message.append("统计时间：").append(alarmResult.getStatisticsTime()).append("\n");
        message.append("异常类型：").append(String.join(",", alarmResult.getAlarmConfig().getCrashTypes())).append("\n\n");

        message.append("触发条件：\n");
        for (String condition : alarmResult.getTriggeredConditions()) {
            message.append("• ").append(condition).append("\n");
        }

        message.append("\n详细数据：\n");
        for (BuglyHallAlarmDataDTO data : alarmResult.getAlarmDataList()) {
            message.append(String.format("应用%s：\n", data.getAppId()));
            message.append(String.format("  - 过去24小时异常设备数：%d\n", data.getPast24HourCrashDeviceCnt()));
            message.append(String.format("  - 过去24小时异常设备率：%.2f%%\n", data.getPast24HourCrashDeviceRate()));
            message.append(String.format("  - 当前小时异常设备数：%d\n", data.getCurrentHourCrashDeviceCnt()));
            message.append(String.format("  - 相比上一日同时段增长率：%.2f%%\n", data.getGrowthRate()));
        }

        return message.toString();
    }

    /**
     * 查询过去24小时设备数据（私有方法）
     *
     * @param appId      应用ID
     * @param crashTypes 异常类型列表
     * @param endTime    结束时间（毫秒时间戳）
     * @return 设备数据
     */
    private BuglyHallAlarmDataDTO queryPast24HourData(String appId, List<String> crashTypes, Long endTime) {
        BuglyHallAlarmDataDTO result = new BuglyHallAlarmDataDTO();
        result.setAppId(appId);
        result.setCrashTypes(crashTypes);

        try {
            Long startTime = endTime - DAY_MILLIS;
            String crashTypeCondition = buildCrashTypeCondition(crashTypes);

            // 查询异常设备数
            BuglyHallAlarmDataDTO crashData = buglyHallAlarmMapper.queryPast24HourCrashData(appId, crashTypeCondition, startTime, endTime);
            Long crashDeviceCount = ObjectUtil.isNotNull(crashData) ? crashData.getPast24HourCrashDeviceCnt() : 0L;
            result.setPast24HourCrashDeviceCnt(crashDeviceCount);

            // 查询总设备数（心跳数据）
            Long totalDeviceCount = buglyHallAlarmMapper.queryPast24HourTotalDeviceCnt(appId, startTime, endTime);
            result.setPast24HourTotalDeviceCnt(ObjectUtil.isNotNull(totalDeviceCount) ? totalDeviceCount : 0L);

            // 计算设备率
            if (result.getPast24HourTotalDeviceCnt() > 0) {
                BigDecimal deviceRate = BigDecimal.valueOf(result.getPast24HourCrashDeviceCnt())
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(result.getPast24HourTotalDeviceCnt()), 2, RoundingMode.HALF_UP);
                result.setPast24HourCrashDeviceRate(deviceRate);
            } else {
                result.setPast24HourCrashDeviceRate(BigDecimal.ZERO);
            }

        } catch (Exception e) {
            log.error("查询过去24小时数据失败，appId: {}, crashTypes: {}", appId, crashTypes, e);
            result.setPast24HourCrashDeviceCnt(0L);
            result.setPast24HourTotalDeviceCnt(0L);
            result.setPast24HourCrashDeviceRate(BigDecimal.ZERO);
        }

        return result;
    }



    /**
     * 查询指定时间段异常设备数（通用方法）
     *
     * @param appId      应用ID
     * @param crashTypes 异常类型列表
     * @param startTime  开始时间（毫秒时间戳）
     * @param endTime    结束时间（毫秒时间戳）
     * @return 异常设备数
     */
    private Long queryHourCrashDeviceCount(String appId, List<String> crashTypes, Long startTime, Long endTime) {
        try {
            String crashTypeCondition = buildCrashTypeCondition(crashTypes);
            Long deviceCount = buglyHallAlarmMapper.queryHourCrashDeviceCnt(appId, crashTypeCondition, startTime, endTime);
            return ObjectUtil.isNotNull(deviceCount) ? deviceCount : 0L;

        } catch (Exception e) {
            log.error("查询指定时间段异常设备数失败，appId: {}, crashTypes: {}, startTime: {}, endTime: {}",
                appId, crashTypes, startTime, endTime, e);
            return 0L;
        }
    }

    /**
     * 发送告警通知
     *
     * @param alarmResult 告警结果
     */
    private void sendAlarmNotification(BuglyHallAlarmResultDTO alarmResult) {
        try {
            String message = alarmResult.getAlarmMessage();
            List<String> receivers = alarmResult.getAlarmConfig().getReceivers();

            // 发送信鸽消息给指定接收人
            if (CollectionUtils.isNotEmpty(receivers)) {
                messageService.sendXingeAdministratorMessage(message, receivers);
            }

            log.info("大厅告警通知发送成功：{}", alarmResult.getAlarmConfig().getAlarmName());

        } catch (Exception e) {
            log.error("发送大厅告警通知失败：{}", alarmResult.getAlarmConfig().getAlarmName(), e);
        }
    }
}
