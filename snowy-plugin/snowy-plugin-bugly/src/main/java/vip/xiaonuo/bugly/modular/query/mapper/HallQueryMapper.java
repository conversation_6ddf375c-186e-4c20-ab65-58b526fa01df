package vip.xiaonuo.bugly.modular.query.mapper;

import org.apache.ibatis.annotations.Param;
import vip.xiaonuo.bugly.modular.monitor.entity.MonitorBuglyHour;

import java.util.List;

/**
 * Description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/8/8 13:46
 */
public interface HallQueryMapper {
    /**
     * 根据时间查询异常数据(天或小时)(Doris)
     *
     * @param startPtDt    开始分区（天）
     * @param endPtDt      结束分区（天）
     * @param startTime    开始时间（毫秒时间戳）
     * @param endTime      结束时间（毫秒时间戳）
     * @param appIds       应用ID列表
     * @param curGameIds   cur_gameid列表
     * @param crashModules crashModule异常模块列表
     * @return 异常数据(天或小时)
     */
    List<MonitorBuglyHour> listExceptionDataMonitor(
            @Param("startPtDt") String startPtDt
            , @Param("endPtDt") String endPtDt
            , @Param("startTime") Long startTime
            , @Param("endTime") Long endTime
            , @Param("appIds") List<String> appIds
            , @Param("curGameIds") List<String> curGameIds
            , @Param("crashModules") List<String> crashModules);

    /**
     * 根据时间查询心跳数据(天或小时)(Doris)
     *
     * @param startPtDt 开始分区（天）
     * @param endPtDt   结束分区（天）
     * @param startTime 开始时间（毫秒时间戳）
     * @param endTime   结束时间（毫秒时间戳）
     * @param appIds    应用ID列表
     * @return 心跳数据(天或小时)
     */
    List<MonitorBuglyHour> listHeartbeatDataMonitor(
            @Param("startPtDt") String startPtDt
            , @Param("endPtDt") String endPtDt
            , @Param("startTime") Long startTime
            , @Param("endTime") Long endTime
            , @Param("appIds") List<String> appIds);

}
