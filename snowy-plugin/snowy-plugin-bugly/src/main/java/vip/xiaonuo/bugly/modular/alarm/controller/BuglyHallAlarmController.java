/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.bugly.modular.alarm.controller;

import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vip.xiaonuo.bugly.modular.alarm.dto.BuglyHallAlarmResultDTO;
import vip.xiaonuo.bugly.modular.alarm.entity.BuglyHallAlarm;
import vip.xiaonuo.bugly.modular.alarm.service.BuglyHallAlarmService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 大厅告警控制器
 *
 * <AUTHOR>
 * @date 2025/08/07 16:00
 **/
@Api(tags = "大厅告警控制器")
@ApiSupport(author = "SNOWY_TEAM", order = 1)
@RestController
@Validated
@RequestMapping("/bugly/hallAlarm")
public class BuglyHallAlarmController {

    @Resource
    private BuglyHallAlarmService buglyHallAlarmService;

    /**
     * 获取大厅告警配置列表
     *
     * <AUTHOR>
     * @date 2025/08/07 16:00
     */
    @ApiOperationSupport(order = 1)
    @ApiOperation("获取大厅告警配置列表")
    @CommonLog("获取大厅告警配置列表")
    @GetMapping("/config/list")
    public CommonResult<List<BuglyHallAlarm>> getConfigList() {
        return CommonResult.data(buglyHallAlarmService.loadAlarmConfigFromJson());
    }

    /**
     * 手动执行大厅告警检测
     *
     * <AUTHOR>
     * @date 2025/08/07 16:00
     */
    @ApiOperationSupport(order = 2)
    @ApiOperation("手动执行大厅告警检测")
    @CommonLog("手动执行大厅告警检测")
    @PostMapping("/execute")
    public CommonResult<List<BuglyHallAlarmResultDTO>> executeAlarm(@RequestParam(required = false) String dayHour) {
        return CommonResult.data(buglyHallAlarmService.sendHallHourAlarm(dayHour));
    }

    /**
     * 测试告警数据查询
     *
     * <AUTHOR>
     * @date 2025/08/07 16:00
     */
    @ApiOperationSupport(order = 3)
    @ApiOperation("测试告警数据查询")
    @CommonLog("测试告警数据查询")
    @GetMapping("/test/data")
    public CommonResult<Object> testDataQuery(@RequestParam @NotBlank String appId,
                                              @RequestParam List<String> crashTypes,
                                              @RequestParam(required = false) String dayHour) {
        return CommonResult.data(buglyHallAlarmService.queryAlarmDataFromDoris(appId, crashTypes, dayHour));
    }
}
