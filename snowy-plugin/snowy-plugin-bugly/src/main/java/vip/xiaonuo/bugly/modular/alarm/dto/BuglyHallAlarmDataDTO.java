/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.bugly.modular.alarm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 大厅告警数据传输对象
 *
 * <AUTHOR>
 * @date 2025/08/07 16:00
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "大厅告警数据传输对象")
public class BuglyHallAlarmDataDTO implements Serializable {

    private static final Long serialVersionUID = 1L;

    /**
     * 应用ID
     */
    @ApiModelProperty(value = "应用ID", name = "appId")
    private String appId;

    /**
     * 异常类型列表
     */
    @ApiModelProperty(value = "异常类型列表", name = "crashTypes")
    private List<String> crashTypes;

    /**
     * 过去24小时异常设备数
     */
    @ApiModelProperty(value = "过去24小时异常设备数", name = "past24HourCrashDeviceCnt")
    private Long past24HourCrashDeviceCnt;

    /**
     * 过去24小时总设备数
     */
    @ApiModelProperty(value = "过去24小时总设备数", name = "past24HourTotalDeviceCnt")
    private Long past24HourTotalDeviceCnt;

    /**
     * 过去24小时异常设备率
     */
    @ApiModelProperty(value = "过去24小时异常设备率", name = "past24HourCrashDeviceRate")
    private BigDecimal past24HourCrashDeviceRate;

    /**
     * 当前小时异常设备数
     */
    @ApiModelProperty(value = "当前小时异常设备数", name = "currentHourCrashDeviceCnt")
    private Long currentHourCrashDeviceCnt;

    /**
     * 上一日同时段异常设备数
     */
    @ApiModelProperty(value = "上一日同时段异常设备数", name = "lastDaySameHourCrashDeviceCnt")
    private Long lastDaySameHourCrashDeviceCnt;

    /**
     * 增长率（百分比）
     */
    @ApiModelProperty(value = "增长率", name = "growthRate")
    private BigDecimal growthRate;

    /**
     * 统计时间
     */
    @ApiModelProperty(value = "统计时间", name = "statisticsTime")
    private String statisticsTime;
}
