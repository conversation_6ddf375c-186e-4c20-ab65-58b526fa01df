package vip.xiaonuo.bugly.modular.query.service;

import vip.xiaonuo.bugly.modular.monitor.entity.MonitorBuglyHour;
import vip.xiaonuo.bugly.modular.syncInfo.entity.AppInfo;

import java.util.List;

/**
 * Description
 *
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025/8/8 11:58
 */
public interface HallQueryService {
    /**
     * 根据时间查询异常数据(天或小时)(汇总数据)(Doris)
     *
     * @param dayHour      时间，若统计类型为小时则默认当前时间减1小时开始时间，若统计类型为天则默认当前时间减1天开始时间，格式yyyy-MM-dd HH:mm:ss
     * @param dayOrHour    =day按天统计，=hour 按小时统计，默认小时
     * @param appIds       应用ID列表
     * @param curGameIds   cur_gameid列表
     * @param crashModules crashModule异常模块列表
     * @return 异常数据(天或小时)(汇总数据)
     */
    List<MonitorBuglyHour> listExceptionDataMonitor(String dayHour, String dayOrHour, List<String> appIds, List<String> curGameIds, List<String> crashModules);

    /**
     * 根据时间查询心跳数据(天或小时)(汇总数据)(Doris)
     *
     * @param dayHour   时间，若统计类型为小时则默认当前时间减1小时开始时间，若统计类型为天则默认当前时间减1天开始时间，格式yyyy-MM-dd HH:mm:ss
     * @param dayOrHour =day按天统计，=hour 按小时统计，默认小时
     * @param appIds    应用ID列表
     * @return 心跳数据(天或小时)(汇总数据)
     */
    List<MonitorBuglyHour> listHeartbeatDataMonitor(String dayHour, String dayOrHour, List<String> appIds);

}
