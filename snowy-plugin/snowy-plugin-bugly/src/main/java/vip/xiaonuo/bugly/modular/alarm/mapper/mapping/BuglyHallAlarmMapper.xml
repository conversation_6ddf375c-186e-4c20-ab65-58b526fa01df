<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.bugly.modular.alarm.mapper.BuglyHallAlarmMapper">

    <!-- 大厅告警数据传输对象类 -->
    <resultMap type="vip.xiaonuo.bugly.modular.alarm.dto.BuglyHallAlarmDataDTO"
               id="buglyHallAlarmDataDTOResultMap">
        <result column="crash_device_cnt" property="past24HourCrashDeviceCnt"/>
        <!-- 其他字段符合驼峰命名，自动匹配 -->
    </resultMap>

<!--    &lt;!&ndash; 查询过去24小时异常设备数据 &ndash;&gt;-->
<!--    <select id="queryPast24HourCrashData" resultMap="buglyHallAlarmDataDTOResultMap">-->
<!--        SELECT -->
<!--            COUNT(DISTINCT crash_deviceId) as crash_device_cnt-->
<!--        FROM jjbugly_crash_crash -->
<!--        WHERE crash_appId = #{appId}-->
<!--        AND crash_crashTime >= #{startTime}-->
<!--        AND crash_crashTime &lt; #{endTime}-->
<!--        <if test="crashTypes != null and crashTypes.trim() != ''">-->
<!--            AND crash_crashType IN (${crashTypes})-->
<!--        </if>-->
<!--    </select>-->

    <!-- 查询过去24小时总设备数 -->
    <select id="queryPast24HourTotalDeviceCnt" resultType="java.lang.Long">
        SELECT 
            COUNT(DISTINCT deviceId) as total_device_cnt
        FROM jjbugly_crash_hb 
        WHERE appId = #{appId}
        AND timeStamp >= #{startTime}
        AND timeStamp &lt; #{endTime}
    </select>

    <!-- 查询指定时间段异常设备数 -->
    <select id="queryHourCrashDeviceCnt" resultType="java.lang.Long">
        SELECT 
            COUNT(DISTINCT crash_deviceId) as crash_device_cnt
        FROM jjbugly_crash_crash 
        WHERE crash_appId = #{appId}
        AND crash_crashTime >= #{startTime}
        AND crash_crashTime &lt; #{endTime}
        <if test="crashTypes != null and crashTypes.trim() != ''">
            AND crash_crashType IN (${crashTypes})
        </if>
    </select>

</mapper>
